<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">Управление лицензиями</h1>
      <button @click="showCreateModal = true" class="btn btn-primary">
        Создать лицензию
      </button>
    </div>

    <!-- Фильтры -->
    <div class="card mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="w-full md:w-auto flex-1">
          <label for="search" class="block text-gray-700 dark:text-gray-300 mb-2">Поиск</label>
          <input
            id="search"
            v-model="searchQuery"
            type="text"
            class="input"
            placeholder="Ключ лицензии"
          />
        </div>

        <div class="w-full md:w-auto md:flex-initial">
          <label for="status-filter" class="block text-gray-700 dark:text-gray-300 mb-2">Статус</label>
          <select id="status-filter" v-model="statusFilter" class="input">
            <option value="all">Все</option>
            <option value="active">Активные</option>
            <option value="inactive">Неактивные</option>
            <option value="expired">Истекшие</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Таблица лицензий -->
    <div class="card overflow-x-auto">
      <div v-if="loading" class="text-center py-8">
        <p>Загрузка лицензий...</p>
      </div>

      <div v-else-if="filteredLicenses.length === 0" class="text-center py-8">
        <p class="text-gray-500 dark:text-gray-400">Лицензии не найдены</p>
      </div>

      <table v-else class="min-w-full">
        <thead>
          <tr class="border-b dark:border-gray-700">
            <th class="py-3 px-4 text-left">ID</th>
            <th class="py-3 px-4 text-left">Ключ</th>
            <th class="py-3 px-4 text-left">Владелец</th>
            <th class="py-3 px-4 text-left">Срок действия</th>
            <th class="py-3 px-4 text-left">Макс. ботов</th>
            <th class="py-3 px-4 text-left">Статус</th>
            <th class="py-3 px-4 text-right">Действия</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="license in filteredLicenses" :key="license.id" class="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
            <td class="py-3 px-4">{{ license.id }}</td>
            <td class="py-3 px-4">{{ license.key }}</td>
            <td class="py-3 px-4">{{ license.owner ? license.owner.username : 'Н/Д' }}</td>
            <td class="py-3 px-4" :class="{ 'text-red-500 dark:text-red-400': isExpired(license) }">
              {{ formatDate(license.expires_at) }}
              <span v-if="isExpired(license)">(истек)</span>
            </td>
            <td class="py-3 px-4">{{ license.max_bots }}</td>
            <td class="py-3 px-4">
              <span
                :class="[
                  'inline-block px-2 py-1 text-xs rounded',
                  getLicenseStatusClass(license)
                ]"
              >
                {{ getLicenseStatusText(license) }}
              </span>
            </td>
            <td class="py-3 px-4 text-right">
              <div class="flex justify-end space-x-2">
                <button
                  @click="toggleLicenseStatus(license)"
                  :class="[
                    'btn',
                    license.is_active ? 'btn-danger' : 'btn-primary'
                  ]"
                >
                  {{ license.is_active ? 'Деактивировать' : 'Активировать' }}
                </button>
                <router-link :to="`/licenses/${license.id}`" class="btn btn-secondary">
                  Детали
                </router-link>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Модальное окно создания лицензии -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
        <h2 class="text-xl font-bold mb-4 dark:text-white">Создание новой лицензии</h2>

        <div v-if="createError" class="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded mb-4">
          <div class="font-bold mb-1">Ошибка:</div>
          <div>{{ createError }}</div>
        </div>

        <form @submit.prevent="createLicense">
          <div class="mb-4">
            <label for="license-key" class="block text-gray-700 dark:text-gray-300 mb-2">Ключ лицензии</label>
            <input
              id="license-key"
              v-model="newLicense.key"
              type="text"
              class="input"
              required
            />
          </div>

          <div class="mb-4">
            <label for="license-owner" class="block text-gray-700 dark:text-gray-300 mb-2">Имя владельца</label>
            <input
              id="license-owner"
              v-model="newLicense.owner_name"
              type="text"
              class="input"
              required
              placeholder="Введите имя владельца"
            />
          </div>

          <div class="mb-4">
            <label for="license-expires" class="block text-gray-700 dark:text-gray-300 mb-2">Срок действия</label>
            <input
              id="license-expires"
              v-model="newLicense.expires_at"
              type="date"
              class="input"
              required
            />
          </div>

          <div class="mb-4">
            <label for="license-max-bots" class="block text-gray-700 dark:text-gray-300 mb-2">Максимальное количество ботов</label>
            <input
              id="license-max-bots"
              v-model.number="newLicense.max_bots"
              type="number"
              class="input"
              required
              min="1"
            />
          </div>

          <div class="flex justify-end space-x-3">
            <button
              type="button"
              @click="showCreateModal = false"
              class="btn btn-secondary"
            >
              Отмена
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="createLoading"
            >
              <span v-if="createLoading">Создание...</span>
              <span v-else>Создать</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useLicensesStore } from '../stores/licenses'

const licensesStore = useLicensesStore()

const loading = computed(() => licensesStore.loading)
const searchQuery = ref('')
const statusFilter = ref('all')
const showCreateModal = ref(false)
const createError = ref('')
const createLoading = ref(false)

const today = new Date()
const nextYear = new Date(today)
nextYear.setFullYear(today.getFullYear() + 1)

const newLicense = ref({
  key: '',
  owner_name: '',
  expires_at: nextYear.toISOString().split('T')[0],
  max_bots: 1,
  is_active: true
})

// Получение списка лицензий при загрузке компонента
onMounted(() => {
  licensesStore.fetchLicenses()
})

// Фильтрация лицензий
const filteredLicenses = computed(() => {
  let result = licensesStore.licenses

  // Фильтр по поисковому запросу
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(license =>
      license.key.toLowerCase().includes(query)
    )
  }

  // Фильтр по статусу
  if (statusFilter.value === 'active') {
    result = result.filter(license => license.is_active && !isExpired(license))
  } else if (statusFilter.value === 'inactive') {
    result = result.filter(license => !license.is_active)
  } else if (statusFilter.value === 'expired') {
    result = result.filter(license => isExpired(license))
  }

  return result
})

// Проверка, истек ли срок действия лицензии
function isExpired(license) {
  return new Date(license.expires_at) < new Date()
}

// Получение класса для статуса лицензии
function getLicenseStatusClass(license) {
  if (!license.is_active) {
    return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  } else if (isExpired(license)) {
    return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
  } else {
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
  }
}

// Получение текста для статуса лицензии
function getLicenseStatusText(license) {
  if (!license.is_active) {
    return 'Неактивна'
  } else if (isExpired(license)) {
    return 'Истекла'
  } else {
    return 'Активна'
  }
}

// Изменение статуса лицензии
async function toggleLicenseStatus(license) {
  const updatedLicense = { ...license, is_active: !license.is_active }
  const result = await licensesStore.updateLicense(updatedLicense)

  if (!result) {
    alert(`Ошибка при ${updatedLicense.is_active ? 'активации' : 'деактивации'} лицензии: ${licensesStore.error}`)
  }
}

// Создание новой лицензии
async function createLicense() {
  createLoading.value = true
  createError.value = ''

  try {
    // Преобразуем дату в формат ISO с временем
    const licenseData = {
      ...newLicense.value,
      expires_at: new Date(newLicense.value.expires_at + 'T00:00:00Z').toISOString()
    }

    console.log('Данные для создания лицензии:', licenseData)
    const result = await licensesStore.createLicense(licenseData)
    if (result) {
      showCreateModal.value = false
      newLicense.value = {
        key: '',
        owner_name: '',
        expires_at: nextYear.toISOString().split('T')[0],
        max_bots: 1,
        is_active: true
      }
    } else {
      createError.value = licensesStore.error || 'Ошибка при создании лицензии'
      console.error('Ошибка из хранилища:', licensesStore.error)
    }
  } catch (error) {
    createError.value = 'Произошла ошибка при создании лицензии'
    console.error('Ошибка создания лицензии:', error)
  } finally {
    createLoading.value = false
  }
}

// Форматирование даты
function formatDate(dateString) {
  if (!dateString) return 'Н/Д'

  const date = new Date(dateString)
  return date.toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  })
}
</script>
