services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tsb-api-server
    restart: unless-stopped
    ports:
      - "8282:8080"
    environment:
      - SERVER_ADDRESS=:8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=tsb_api
      - DB_SSLMODE=disable
      - JWT_SECRET=your-secret-key
      - JWT_EXPIRES_IN=24h
      - LOG_LEVEL=info
    depends_on:
      - postgres
    networks:
      - tsb-network

  postgres:
    image: postgres:14-alpine
    container_name: tsb-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=tsb_api
    volumes:
      - postgres-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - tsb-network

  admin-panel:
    build:
      context: ./web/admin
      dockerfile: Dockerfile
    container_name: tsb-admin-panel
    restart: unless-stopped
    ports:
      - "8081:80"
    depends_on:
      - api
    networks:
      - tsb-network

networks:
  tsb-network:
    driver: bridge

volumes:
  postgres-data:
