# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Environment variables
.env
.env.local

# Logs
*.log

# Docker volumes
data/

# Node modules for frontend
web/admin/node_modules/
web/admin/dist/

# OS specific
.DS_Store
