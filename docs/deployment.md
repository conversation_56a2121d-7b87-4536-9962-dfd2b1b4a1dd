# Инструкция по развертыванию

## Требования

- <PERSON><PERSON> и Docker Compose
- Nginx Proxy Manager (для обратного прокси и HTTPS)
- Доступ к серверу через SSH

## Подготовка к развертыванию

1. Клонируйте репозиторий на сервер:

```bash
git clone https://github.com/kirillz/tsb-api-server.git
cd tsb-api-server
```

2. Создайте файл `.env` на основе `.env.example`:

```bash
cp .env.example .env
```

3. Отредактируйте файл `.env`, установив безопасные значения для:
   - `JWT_SECRET` - секретный ключ для JWT токенов
   - `DB_PASSWORD` - пароль для базы данных
   - Другие настройки по необходимости

## Развертывание с помощью Docker Compose

1. Запустите приложение в режиме демона:

```bash
docker-compose up -d
```

2. Проверьте, что все контейнеры запущены:

```bash
docker-compose ps
```

Вы должны увидеть три контейнера:
- `tsb-api-server` - API сервер на Go
- `tsb-postgres` - база данных PostgreSQL
- `tsb-admin-panel` - панель администрирования на Vue.js

## Настройка Nginx Proxy Manager

1. Войдите в панель управления Nginx Proxy Manager.

2. Добавьте новый прокси-хост для API сервера:
   - Домен: `api.yourdomain.com` (замените на ваш домен)
   - Схема: `http`
   - IP-адрес/хост: `tsb-api-server`
   - Порт: `8081`
   - Включите SSL и настройте Let's Encrypt

3. Добавьте новый прокси-хост для панели администрирования:
   - Домен: `admin.yourdomain.com` (замените на ваш домен)
   - Схема: `http`
   - IP-адрес/хост: `tsb-admin-panel`
   - Порт: `80`
   - Включите SSL и настройте Let's Encrypt

## Создание первого пользователя-администратора

Для создания первого пользователя-администратора выполните следующую команду:

```bash
docker-compose exec api /app/api-server create-admin
```

Следуйте инструкциям для создания учетной записи администратора.

## Обновление приложения

1. Остановите текущие контейнеры:

```bash
docker-compose down
```

2. Получите последние изменения из репозитория:

```bash
git pull
```

3. Пересоберите и запустите контейнеры:

```bash
docker-compose up -d --build
```

## Резервное копирование базы данных

1. Создание резервной копии:

```bash
docker-compose exec postgres pg_dump -U postgres tsb_api > backup_$(date +%Y%m%d).sql
```

2. Восстановление из резервной копии:

```bash
cat backup_YYYYMMDD.sql | docker-compose exec -T postgres psql -U postgres tsb_api
```

## Мониторинг и логи

Просмотр логов API сервера:

```bash
docker-compose logs -f api
```

Просмотр логов базы данных:

```bash
docker-compose logs -f postgres
```

Просмотр логов панели администрирования:

```bash
docker-compose logs -f admin-panel
```

## Устранение неполадок

### Проблема: API сервер не запускается

1. Проверьте логи:

```bash
docker-compose logs api
```

2. Убедитесь, что база данных доступна:

```bash
docker-compose exec api ping -c 1 postgres
```

### Проблема: Панель администрирования не подключается к API

1. Проверьте, что API сервер работает:

```bash
curl http://localhost:8080/api/health
```

2. Проверьте настройки прокси в Nginx Proxy Manager.

### Проблема: Ошибка подключения к базе данных

1. Проверьте, что контейнер с базой данных запущен:

```bash
docker-compose ps postgres
```

2. Проверьте логи базы данных:

```bash
docker-compose logs postgres
```

3. Убедитесь, что настройки подключения в `.env` файле корректны.
