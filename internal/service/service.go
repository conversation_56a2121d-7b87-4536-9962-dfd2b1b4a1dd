package service

import (
	"context"

	"github.com/kirillz/tsb-api-server/internal/config"
	"github.com/kirillz/tsb-api-server/internal/model"
	"github.com/kirillz/tsb-api-server/internal/repository"
)

// Deps содержит зависимости для сервисов
type Deps struct {
	Repos *repository.Repositories
	JWT   config.JWTConfig
}

// Services содержит все сервисы
type Services struct {
	Auth       AuthService
	Bot        BotService
	License    LicenseService
	Statistics StatisticsService
	User       UserService
}

// AuthService интерфейс для аутентификации
type AuthService interface {
	Login(ctx context.Context, username, password string) (string, error)
	ValidateToken(token string) (*model.User, error)
}

// BotService интерфейс для работы с ботами
type BotService interface {
	Register(ctx context.Context, req *model.RegisterBotRequest) (*model.Bot, error)
	GetStatus(ctx context.Context, botID uint) (*model.BotStatusResponse, error)
	UpdateStatus(ctx context.Context, botID uint, isActive bool) error
	GetByID(ctx context.Context, botID uint) (*model.Bot, error)
	GetAll(ctx context.Context) ([]*model.Bot, error)
	Delete(ctx context.Context, botID uint) error
	UpdateLastActivity(ctx context.Context, botID uint) error
}

// LicenseService интерфейс для работы с лицензиями
type LicenseService interface {
	Check(ctx context.Context, licenseKey string) (*model.LicenseCheckResponse, error)
	Create(ctx context.Context, license *model.License) error
	CreateWithOwnerName(ctx context.Context, key, ownerName, expiresAt string, maxBots int, isActive bool) (*model.License, error)
	GetByID(ctx context.Context, licenseID uint) (*model.License, error)
	GetAll(ctx context.Context) ([]*model.License, error)
	Update(ctx context.Context, license *model.License) error
	Delete(ctx context.Context, licenseID uint) error
	GetByOwnerID(ctx context.Context, ownerID uint) ([]*model.License, error)
}

// StatisticsService интерфейс для работы со статистикой
type StatisticsService interface {
	Save(ctx context.Context, req *model.StatisticsRequest) error
	GetByBotID(ctx context.Context, botID uint) ([]*model.Statistics, error)
	GetAll(ctx context.Context) ([]*model.Statistics, error)
}

// UserService интерфейс для работы с пользователями
type UserService interface {
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, userID uint) (*model.User, error)
	GetByUsername(ctx context.Context, username string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, userID uint) error
}

// NewServices создает новые сервисы
func NewServices(deps Deps) *Services {
	return &Services{
		Auth:       NewAuthService(deps.Repos.User, deps.JWT),
		Bot:        NewBotService(deps.Repos.Bot, deps.Repos.License),
		License:    NewLicenseService(deps.Repos.License, deps.Repos.Bot, deps.Repos.User),
		Statistics: NewStatisticsService(deps.Repos.Statistics, deps.Repos.Bot),
		User:       NewUserService(deps.Repos.User),
	}
}
