package service

import (
	"context"
	"errors"
	"time"

	"github.com/kirillz/tsb-api-server/internal/model"
	"github.com/kirillz/tsb-api-server/internal/repository"
)

// LicenseService реализация LicenseService
type licenseService struct {
	licenseRepo repository.LicenseRepository
	botRepo     repository.BotRepository
	userRepo    repository.UserRepository
}

// NewLicenseService создает новый сервис лицензий
func NewLicenseService(licenseRepo repository.LicenseRepository, botRepo repository.BotRepository, userRepo repository.UserRepository) LicenseService {
	return &licenseService{
		licenseRepo: licenseRepo,
		botRepo:     botRepo,
		userRepo:    userRepo,
	}
}

// Check проверяет валидность лицензии
func (s *licenseService) Check(ctx context.Context, licenseKey string) (*model.LicenseCheckResponse, error) {
	license, err := s.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		return nil, err
	}
	if license == nil {
		return &model.LicenseCheckResponse{
			IsValid: false,
			Message: "лицензия не найдена",
		}, nil
	}

	if !license.IsActive {
		return &model.LicenseCheckResponse{
			IsValid: false,
			Message: "лицензия неактивна",
		}, nil
	}

	if license.ExpiresAt.Before(time.Now()) {
		return &model.LicenseCheckResponse{
			IsValid: false,
			Message: "срок действия лицензии истек",
		}, nil
	}

	// Проверка существования лицензии достаточно для валидации
	// Дополнительные проверки не требуются

	return &model.LicenseCheckResponse{
		IsValid:   true,
		ExpiresAt: license.ExpiresAt,
		MaxBots:   license.MaxBots,
		Message:   "лицензия действительна",
	}, nil
}

// Create создает новую лицензию
func (s *licenseService) Create(ctx context.Context, license *model.License) error {
	// Проверка существования лицензии с таким ключом
	existingLicense, err := s.licenseRepo.GetByKey(ctx, license.Key)
	if err != nil {
		return err
	}
	if existingLicense != nil {
		return errors.New("лицензия с таким ключом уже существует")
	}

	// Установка значений по умолчанию
	if license.MaxBots <= 0 {
		license.MaxBots = 1
	}
	if license.ExpiresAt.IsZero() {
		license.ExpiresAt = time.Now().AddDate(1, 0, 0) // По умолчанию 1 год
	}
	license.IsActive = true

	return s.licenseRepo.Create(ctx, license)
}

// CreateWithOwnerName создает новую лицензию по имени владельца
func (s *licenseService) CreateWithOwnerName(ctx context.Context, key, ownerName, expiresAt string, maxBots int, isActive bool) (*model.License, error) {
	// Поиск пользователя по имени
	user, err := s.userRepo.GetByUsername(ctx, ownerName)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("пользователь с таким именем не найден")
	}

	// Проверка существования лицензии с таким ключом
	existingLicense, err := s.licenseRepo.GetByKey(ctx, key)
	if err != nil {
		return nil, err
	}
	if existingLicense != nil {
		return nil, errors.New("лицензия с таким ключом уже существует")
	}

	// Парсинг даты истечения
	var expiresAtTime time.Time
	if expiresAt != "" {
		expiresAtTime, err = time.Parse(time.RFC3339, expiresAt)
		if err != nil {
			return nil, errors.New("неверный формат даты истечения")
		}
	} else {
		expiresAtTime = time.Now().AddDate(1, 0, 0) // По умолчанию 1 год
	}

	// Установка значений по умолчанию
	if maxBots <= 0 {
		maxBots = 1
	}

	// Создание лицензии
	license := &model.License{
		Key:       key,
		OwnerID:   user.ID,
		ExpiresAt: expiresAtTime,
		MaxBots:   maxBots,
		IsActive:  isActive,
	}

	err = s.licenseRepo.Create(ctx, license)
	if err != nil {
		return nil, err
	}

	// Загружаем лицензию с владельцем для возврата
	return s.licenseRepo.GetByID(ctx, license.ID)
}

// GetByID получает лицензию по ID
func (s *licenseService) GetByID(ctx context.Context, licenseID uint) (*model.License, error) {
	license, err := s.licenseRepo.GetByID(ctx, licenseID)
	if err != nil {
		return nil, err
	}
	if license == nil {
		return nil, errors.New("лицензия не найдена")
	}

	return license, nil
}

// GetAll получает все лицензии
func (s *licenseService) GetAll(ctx context.Context) ([]*model.License, error) {
	return s.licenseRepo.GetAll(ctx)
}

// Update обновляет лицензию
func (s *licenseService) Update(ctx context.Context, license *model.License) error {
	existingLicense, err := s.licenseRepo.GetByID(ctx, license.ID)
	if err != nil {
		return err
	}
	if existingLicense == nil {
		return errors.New("лицензия не найдена")
	}

	// Проверка существования лицензии с таким ключом (если ключ изменился)
	if license.Key != existingLicense.Key {
		licenseWithSameKey, err := s.licenseRepo.GetByKey(ctx, license.Key)
		if err != nil {
			return err
		}
		if licenseWithSameKey != nil && licenseWithSameKey.ID != license.ID {
			return errors.New("лицензия с таким ключом уже существует")
		}
	}

	return s.licenseRepo.Update(ctx, license)
}

// Delete удаляет лицензию
func (s *licenseService) Delete(ctx context.Context, licenseID uint) error {
	// Проверка существования лицензии
	license, err := s.licenseRepo.GetByID(ctx, licenseID)
	if err != nil {
		return err
	}
	if license == nil {
		return errors.New("лицензия не найдена")
	}

	// Проверка наличия ботов, использующих эту лицензию
	botCount, err := s.licenseRepo.CountBotsByLicenseID(ctx, licenseID)
	if err != nil {
		return err
	}
	if botCount > 0 {
		return errors.New("невозможно удалить лицензию, которая используется ботами")
	}

	return s.licenseRepo.Delete(ctx, licenseID)
}

// GetByOwnerID получает лицензии по ID владельца
func (s *licenseService) GetByOwnerID(ctx context.Context, ownerID uint) ([]*model.License, error) {
	return s.licenseRepo.GetByOwnerID(ctx, ownerID)
}
