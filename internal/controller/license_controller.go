package controller

import (
	"github.com/gofiber/fiber/v2"
	"github.com/kirillz/tsb-api-server/internal/model"
	"github.com/kirillz/tsb-api-server/internal/service"
)

// LicenseController контроллер для лицензий
type LicenseController interface {
	Check(c *fiber.Ctx) error
	Create(c *fiber.Ctx) error
	GetAll(c *fiber.Ctx) error
	GetByID(c *fiber.Ctx) error
	Update(c *fiber.Ctx) error
	Delete(c *fiber.Ctx) error
}

type licenseController struct {
	licenseService service.LicenseService
}

// NewLicenseController создает новый контроллер лицензий
func NewLicenseController(licenseService service.LicenseService) LicenseController {
	return &licenseController{
		licenseService: licenseService,
	}
}

// Check обрабатывает запрос на проверку лицензии
func (ctrl *licenseController) Check(c *fiber.Ctx) error {
	licenseKey := c.Query("key")
	if licenseKey == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "ключ лицензии обязателен",
		})
	}

	// Проверка лицензии
	response, err := ctrl.licenseService.Check(c.Context(), licenseKey)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(response)
}

// Create обрабатывает запрос на создание лицензии
func (ctrl *licenseController) Create(c *fiber.Ctx) error {
	var req struct {
		Key       string `json:"key"`
		OwnerName string `json:"owner_name"`
		ExpiresAt string `json:"expires_at"`
		MaxBots   int    `json:"max_bots"`
		IsActive  bool   `json:"is_active"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "неверный формат запроса",
		})
	}

	// Валидация запроса
	if req.Key == "" || req.OwnerName == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "ключ лицензии и имя владельца обязательны",
		})
	}

	// Создание лицензии
	license, err := ctrl.licenseService.CreateWithOwnerName(c.Context(), req.Key, req.OwnerName, req.ExpiresAt, req.MaxBots, req.IsActive)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusCreated).JSON(license)
}

// GetAll обрабатывает запрос на получение всех лицензий
func (ctrl *licenseController) GetAll(c *fiber.Ctx) error {
	licenses, err := ctrl.licenseService.GetAll(c.Context())
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(licenses)
}

// GetByID обрабатывает запрос на получение лицензии по ID
func (ctrl *licenseController) GetByID(c *fiber.Ctx) error {
	licenseID, err := c.ParamsInt("id")
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "неверный ID лицензии",
		})
	}

	license, err := ctrl.licenseService.GetByID(c.Context(), uint(licenseID))
	if err != nil {
		return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(license)
}

// Update обрабатывает запрос на обновление лицензии
func (ctrl *licenseController) Update(c *fiber.Ctx) error {
	var license model.License
	if err := c.BodyParser(&license); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "неверный формат запроса",
		})
	}

	// Валидация запроса
	if license.ID == 0 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "ID лицензии обязателен",
		})
	}

	// Обновление лицензии
	err := ctrl.licenseService.Update(c.Context(), &license)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.JSON(license)
}

// Delete обрабатывает запрос на удаление лицензии
func (ctrl *licenseController) Delete(c *fiber.Ctx) error {
	licenseID, err := c.ParamsInt("id")
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "неверный ID лицензии",
		})
	}

	err = ctrl.licenseService.Delete(c.Context(), uint(licenseID))
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": err.Error(),
		})
	}

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": "лицензия успешно удалена",
	})
}
