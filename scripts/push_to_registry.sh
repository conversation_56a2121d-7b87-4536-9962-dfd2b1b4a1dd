#!/bin/bash

set -e  # Выход при ошибке

# Переходим в корневую директорию проекта
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

echo "🔧 Проверка и настройка Docker buildx..."

# Проверяем, установлен ли buildx
if ! docker buildx version >/dev/null 2>&1; then
    echo "❌ Docker buildx не установлен. Устанавливаем..."
    # Для большинства современных версий Docker buildx уже включен
    # Если нет, можно установить как плагин
    echo "Пожалуйста, обновите Docker до последней версии или установите buildx плагин"
    exit 1
fi

# Создаем и используем новый builder (если не существует)
BUILDER_NAME="tsb-builder"
if ! docker buildx inspect $BUILDER_NAME >/dev/null 2>&1; then
    echo "📦 Создаем новый buildx builder: $BUILDER_NAME"
    docker buildx create --name $BUILDER_NAME --use
else
    echo "✅ Используем существующий builder: $BUILDER_NAME"
    docker buildx use $BUILDER_NAME
fi

# Убеждаемся, что builder запущен
docker buildx inspect --bootstrap

# Сначала собираем образ локально
echo "🏗️  Собираем образ локально с помощью buildx..."
docker buildx build \
    --platform linux/amd64 \
    --tag tsb-api-server:latest \
    --load \
    -f Dockerfile \
    .

# Тегируем для реестра
echo "🏷️  Создаем тег для реестра..."
docker tag tsb-api-server:latest reg.revive-it.ru/tsb-api-server:latest

# Проверяем авторизацию в реестре
echo "🔐 Проверяем подключение к реестру..."
if ! docker login reg.revive-it.ru; then
    echo "❌ Ошибка авторизации в реестре. Пожалуйста, выполните 'docker login reg.revive-it.ru' вручную"
    exit 1
fi

# Отправляем в реестр
echo "📤 Отправляем образ в реестр..."
docker push reg.revive-it.ru/tsb-api-server:latest

echo "✅ Образ успешно собран и отправлен в реестр!"
echo "📦 Образ доступен по адресу: reg.revive-it.ru/tsb-api-server:latest"
