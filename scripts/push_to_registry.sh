#!/bin/bash

set -e  # Выход при ошибке

echo "🔧 Проверка и настройка Docker buildx..."

# Проверяем, установлен ли buildx
if ! docker buildx version >/dev/null 2>&1; then
    echo "❌ Docker buildx не установлен. Устанавливаем..."
    # Для большинства современных версий Docker buildx уже включен
    # Если нет, можно установить как плагин
    echo "Пожалуйста, обновите Docker до последней версии или установите buildx плагин"
    exit 1
fi

# Создаем и используем новый builder (если не существует)
BUILDER_NAME="tsb-builder"
if ! docker buildx inspect $BUILDER_NAME >/dev/null 2>&1; then
    echo "📦 Создаем новый buildx builder: $BUILDER_NAME"
    docker buildx create --name $BUILDER_NAME --use
else
    echo "✅ Используем существующий builder: $BUILDER_NAME"
    docker buildx use $BUILDER_NAME
fi

# Убеждаемся, что builder запущен
docker buildx inspect --bootstrap

echo "🏗️  Собираем образ с помощью buildx..."

# Собираем и пушим образ одной командой с использованием buildx
docker buildx build \
    --platform linux/amd64 \
    --tag tsb-api-server:latest \
    --tag reg.revive-it.ru/tsb-api-server:latest \
    --push \
    ..

echo "✅ Образ успешно собран и отправлен в реестр!"
echo "📦 Образ доступен по адресу: reg.revive-it.ru/tsb-api-server:latest"
